import copy
import numpy as np
import matplotlib.pyplot as plt
import numpy.random as rnd
import matplotlib
# from haversine import haversine
import random
import math
import os
import pandas as pd
import time
from datetime import datetime, timedelta

line1='1 1 2 2 2 3 3 4 4 4 4 3 3 4 5 6 7 7 8 10 11 12 12 11 11 12 11 10 7 6 5 4 4 4 4 4 5 4 4 5 5 5 5 6 4 4 4 3 5 5 5 5 5 7 8 8 9 9 10 11 11 12 11 10 10 9 8 8 8 8 7 7 5 7 7 7 5 5 6 6 5 4 5 5 4 4 4 5 5 5 4 4 3 3 3 3 3 2 2 3 3 3 2 2 2 2 2 2 2 2 3 2 2 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 2 1 1 2 2'
line2='1 1 1 1 1 1 1 1 1 0 0 1 2 2 2 3 4 5 7 9 13 13 13 14 14 13 11 9 8 8 7 6  6 5 5 3 4 3 3 2 2 2 2 2 1 1 1 1 1 2 2 2 3 3 3 3 5 6 8 10 9 9 9 10 9 8 7 6 6 5 5 4 4 4 4 3 2 2 3 2 2 1 1 1 1 2 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0'
line3='20 26 25 28 26 35 36 33 36 27 38 49 64 76 82 105 151 196 263 289 285 268 255 246 230 194 186 130 124 107 99 70 66 60 73 71 68 59 51 55 51 62 54 50 36 39 47 51 64 56 60 70 91 98 122 114 134 156 174 183 173 184 195 178 177 151 153 131 119 98 78 84 83 91 75 60 58 63 70 65 53 54 53 55 39 44 37 44 38 31 24 29 23 24 20 13 10 13 10 21 23 17 14 15 17 18 15 8 7 10 10 9 6 6 7 12 10 9 4 4 4 3 3 1 2 3 3 5 6 9 12 7 11 10 8 9 12 4  '
line4='3 3 1 3 2 8 6 7 5 2 9 5 7 11 18 15 16 37 81 90 94 94 83 76 71 60 36 21 24 24 16 9 12 8 13 12 7 12 7 4 6 9 13 4 1 5 7 10 8 11 6 8 15 19 29 22 24 39 41 36 43 54 49 51 39 37 24 25 22 17 7 10 12 10 14 10 5 7 14 7 10 7 11 10 6 3 6 8 5 7 2 6 2 5 1 2 3 0 0 7 2 1 3 1 2 3 2 0 0 3 3 2 1 0 1 0 1 3 2 0 0 1 0 0 0 1 2 0 0 3 1 1 1 1 0 2 2 0  '
num_adv = list(map(int, line1.split()))
num_cou = list(map(int, line2.split()))
num_OAs = list(map(int, line3.split()))
num_unpicked_order = list(map(int, line4.split()))
end_stage = len(num_OAs)


start_time = datetime.strptime("08:00", "%H:%M")
time_intervals = [start_time + timedelta(minutes=10 * i) for i in range(end_stage)]
time_labels = [time.strftime("%H:%M") for time in time_intervals]

# 设置全局字体样式为 Times New Roman
plt.rcParams.update({'font.size': 14, 'font.family': 'Times New Roman'})

# 创建图形和轴
fig, ax1 = plt.subplots(figsize=(10, 6))

# 绘制第一个 y 轴的两条曲线
ax1.plot(time_labels, num_OAs, label='Number of Orders', color='#f79059', linestyle='-', linewidth=2,
            markersize=6)
ax1.plot(time_labels, num_unpicked_order, label='Unpicked Orders', color='#9bbf8a', linestyle='-',
            linewidth=2, markersize=6)

# 设置第一个 y 轴
ax1.set_xlabel('Time', fontsize=16)
ax1.set_ylabel('Number of Orders', fontsize=16)
ax1.tick_params(axis='y', labelcolor='black')
#ax1.set_xticklabels(time_labels, rotation=45, ha='right')
ax1.set_xticks(time_labels[::5])
ax1.set_xticklabels(time_labels[::5])
#ax1.grid(True, which='both', linestyle='--', linewidth=0.7, alpha=0.6)

# 创建第二个 y 轴
ax2 = ax1.twinx()

# 绘制第二个 y 轴的曲线
ax2.plot(time_labels, num_adv, label='Num of ADV', color='#c82423', linestyle='--', linewidth=2,
            markersize=6)
ax2.plot(time_labels, num_cou, label='Num of Cou', color='#3480b8', linestyle='--', linewidth=2,
            markersize=6)

# 设置第二个 y 轴
ax2.set_ylabel('Number of vehicles', fontsize=16)
ax2.tick_params(axis='y')

# 添加标题和图例
#plt.title('Fluctuations of delivery-related parameters', fontsize=18, fontweight='bold')
ax1.legend(loc='upper left', fontsize=12)
ax2.legend(loc='upper right', fontsize=12)

# 调整布局
fig.tight_layout()

# 显示图形
plt.show()
ave_oa=sum(num_OAs) / 72
ave_unpick=sum(num_unpicked_order)/72
print(sum(num_OAs))
print(sum(num_unpicked_order))

# 第一张图：订单相关
l1 = ax1.plot(time_labels, num_OAs, label='Number of Orders', color='#f79059', linestyle='-', linewidth=2)
l2 = ax1.plot(time_labels, num_unpicked_order, label='Unpicked Orders', color='#9bbf8a', linestyle='-', linewidth=2)
# 整点特殊标注
ax1.scatter([time_labels[i] for i in hour_ticks], [num_OAs[i] for i in hour_ticks], marker='*', color='#f79059', s=120, zorder=5)
ax1.scatter([time_labels[i] for i in hour_ticks], [num_unpicked_order[i] for i in hour_ticks], marker='D', color='#9bbf8a', s=80, zorder=5)
ax1.set_ylabel('Number of Orders', fontsize=16)
ax1.tick_params(axis='both', labelsize=16)
ax1.legend(fontsize=14, loc='upper left')
ax1.set_xticks(hour_labels)
ax1.set_xticklabels(hour_labels, fontsize=16)

# 第二张图：车辆相关
l3 = ax2.plot(time_labels, num_adv, label='Num of ADV', color='#c82423', linestyle='--', linewidth=2)
l4 = ax2.plot(time_labels, num_cou, label='Num of Cou', color='#3480b8', linestyle='--', linewidth=2)
# 整点特殊标注
ax2.scatter([time_labels[i] for i in hour_ticks], [num_adv[i] for i in hour_ticks], marker='P', color='#c82423', s=100, zorder=5)
ax2.scatter([time_labels[i] for i in hour_ticks], [num_cou[i] for i in hour_ticks], marker='X', color='#3480b8', s=100, zorder=5)
ax2.set_ylabel('Number of vehicles', fontsize=16)
ax2.set_xlabel('Time', fontsize=16)
ax2.tick_params(axis='both', labelsize=16)
ax2.legend(fontsize=14, loc='upper right')
ax2.set_xticks(hour_labels)
ax2.set_xticklabels(hour_labels, fontsize=16)